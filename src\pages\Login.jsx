import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { FaEnvelope, FaLock, FaEye, FaEyeSlash, FaGoogle, FaFacebook, FaApple, FaArrowLeft, FaUser } from 'react-icons/fa'

// Container principal avec design moderne et propre
const LoginPageContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  padding: 1rem;
  position: relative;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
`

// Conteneur principal de la carte de connexion
const LoginCard = styled.div`
  background: white;
  border-radius: 16px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 400px;
  padding: 2rem;
  position: relative;
  z-index: 10;

  @media (max-width: 768px) {
    padding: 1.5rem;
    margin: 1rem;
    border-radius: 12px;
  }
`

// En-tête avec logo et titre
const LoginHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`

const Logo = styled.div`
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #449aa4 0%, #2c7a7b 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;

  svg {
    font-size: 1.5rem;
    color: white;
  }
`

const Title = styled.h1`
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.25rem;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`

const Subtitle = styled.p`
  color: #718096;
  font-size: 0.875rem;
  margin-bottom: 0;
  font-weight: 400;
`



// Styles pour le formulaire
const LoginForm = styled.form`
  display: flex;
  flex-direction: column;
`

const FormGroup = styled.div`
  margin-bottom: 1.25rem;
`

const FormLabel = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`

const InputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;

  svg:first-child {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1rem;
    z-index: 2;
    pointer-events: none;
  }
`

const InputField = styled.input`
  width: 100%;
  padding: 0.75rem 2.75rem 0.75rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &::placeholder {
    color: #9ca3af;
    font-weight: 400;
  }

  &:focus {
    outline: none;
    border-color: #449aa4;
    box-shadow: 0 0 0 3px rgba(68, 154, 164, 0.1);
  }

  &:hover {
    border-color: #9ca3af;
  }

  /* Style spécial pour les champs sans bouton toggle */
  &:not([type="password"]):not([data-has-toggle="true"]) {
    padding-right: 0.75rem;
  }
`

const PasswordToggle = styled.button`
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;

  &:hover {
    color: #449aa4;
    background-color: rgba(68, 154, 164, 0.1);
  }

  &:focus {
    outline: none;
    color: #449aa4;
    background-color: rgba(68, 154, 164, 0.1);
  }

  svg {
    font-size: 0.875rem;
  }
`

// Boutons sociaux
const SocialLoginSection = styled.div`
  margin-bottom: 1.5rem;
`

const SocialLoginText = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  text-align: center;
  font-weight: 400;
`

const SocialButtons = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 0.5rem;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
`

const SocialButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  font-weight: 500;
  gap: 0.5rem;

  &:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
  }

  &:active {
    transform: scale(0.98);
  }

  &.google {
    color: #ea4335;
  }

  &.facebook {
    color: #1877f2;
  }

  &.apple {
    color: #000;
  }
`

const OrDivider = styled.div`
  display: flex;
  align-items: center;
  margin: 1.5rem 0;

  &::before, &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e5e7eb;
  }

  span {
    padding: 0 1rem;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 400;
    background: white;
  }
`

// Autres éléments du formulaire
const RememberForgot = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
`

const RememberMe = styled.label`
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
  font-weight: 400;

  input {
    margin-right: 0.5rem;
    accent-color: #449aa4;
  }
`

const ForgotPassword = styled(Link)`
  color: #449aa4;
  font-weight: 500;
  font-size: 0.875rem;
  text-decoration: none;
  transition: all 0.2s ease;

  &:hover {
    text-decoration: underline;
  }
`

const LoginButton = styled.button`
  width: 100%;
  background: #449aa4;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1rem;

  &:hover {
    background: #2c7a7b;
  }

  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }
`

const RegisterLink = styled.div`
  text-align: center;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: #6b7280;

  a {
    color: #449aa4;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover {
      text-decoration: underline;
    }
  }
`

const ErrorMessage = styled.div`
  background: #fef2f2;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #fecaca;
`

const SuccessMessage = styled.div`
  background: #f0fdf4;
  color: #16a34a;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #bbf7d0;
`

// Composant principal
const Login = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  })
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    // Clear messages when user starts typing
    if (error) setError('')
    if (success) setSuccess('')
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setSuccess('')

    // Validation
    if (!formData.email || !formData.password) {
      setError('Veuillez remplir tous les champs obligatoires')
      setIsLoading(false)
      return
    }

    // Validation email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      setError('Veuillez entrer une adresse email valide')
      setIsLoading(false)
      return
    }

    // Validation mot de passe
    if (formData.password.length < 6) {
      setError('Le mot de passe doit contenir au moins 6 caractères')
      setIsLoading(false)
      return
    }

    // Simulation de connexion réussie
    setTimeout(() => {
      setSuccess('Connexion réussie ! Redirection en cours...')
      setTimeout(() => {
        setIsLoading(false)
        navigate('/')
      }, 1000)
    }, 1500)
  }

  const handleSocialLogin = (provider) => {
    console.log(`Connexion avec ${provider}`)
    setSuccess(`Redirection vers ${provider}...`)
    // Dans une application réelle, vous utiliseriez OAuth ici
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  return (
    <LoginPageContainer>
      <LoginCard>
        <LoginHeader>
          <Logo>
            <FaUser />
          </Logo>
          <Title>ZiriTravel</Title>
          <Subtitle>Connectez-vous à votre compte</Subtitle>
        </LoginHeader>

        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}

        <SocialLoginSection>
          <SocialLoginText>Connexion rapide avec</SocialLoginText>
          <SocialButtons>
            <SocialButton
              type="button"
              className="google"
              onClick={() => handleSocialLogin('Google')}
            >
              <FaGoogle />
              Google
            </SocialButton>
            <SocialButton
              type="button"
              className="facebook"
              onClick={() => handleSocialLogin('Facebook')}
            >
              <FaFacebook />
              Facebook
            </SocialButton>
            <SocialButton
              type="button"
              className="apple"
              onClick={() => handleSocialLogin('Apple')}
            >
              <FaApple />
              Apple
            </SocialButton>
          </SocialButtons>
        </SocialLoginSection>

        <OrDivider>
          <span>ou avec votre email</span>
        </OrDivider>

        <LoginForm onSubmit={handleSubmit}>
          <FormGroup>
            <FormLabel>Adresse email</FormLabel>
            <InputWrapper>
              <FaEnvelope />
              <InputField
                type="email"
                name="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </InputWrapper>
          </FormGroup>

          <FormGroup>
            <FormLabel>Mot de passe</FormLabel>
            <InputWrapper>
              <FaLock />
              <InputField
                type={showPassword ? "text" : "password"}
                name="password"
                placeholder="Votre mot de passe"
                value={formData.password}
                onChange={handleChange}
                data-has-toggle="true"
                required
              />
              <PasswordToggle
                type="button"
                onClick={togglePasswordVisibility}
                aria-label="Afficher/masquer le mot de passe"
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </PasswordToggle>
            </InputWrapper>
          </FormGroup>

          <RememberForgot>
            <RememberMe>
              <input
                type="checkbox"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleChange}
              />
              <span>Se souvenir de moi</span>
            </RememberMe>
            <ForgotPassword to="/forgot-password">
              Mot de passe oublié ?
            </ForgotPassword>
          </RememberForgot>

          <LoginButton type="submit" disabled={isLoading}>
            {isLoading ? "Connexion en cours..." : "Se connecter"}
          </LoginButton>
        </LoginForm>

        <RegisterLink>
          Vous n'avez pas de compte ? <Link to="/register">Créer un compte</Link>
        </RegisterLink>
      </LoginCard>
    </LoginPageContainer>
  )
}

export default Login

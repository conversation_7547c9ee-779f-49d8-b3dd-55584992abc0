import { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import styled from 'styled-components'
import { destinations } from '../data/destinations'
import { FaSearch, FaCalendarAlt, FaStar, FaMapMarkerAlt, FaClock, FaFilter, FaSortAmountDown } from 'react-icons/fa'

// Section héro avec image d'arrière-plan
const HeroSection = styled.section`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5)),
    url('https://images.pexels.com/photos/32350630/pexels-photo-32350630.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  z-index: -1;

  @media (max-width: 768px) {
    background-attachment: scroll;
  }
`

const PageWrapper = styled.div`
  position: relative;
  z-index: 1;
  min-height: 100vh;
  background: transparent;
  padding-top: 2rem;
`

const PageHeader = styled.div`
  text-align: center;
  padding: 3rem 2rem;
  margin-bottom: 2rem;

  h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    @supports not (-webkit-background-clip: text) {
      color: #FFD700;
      background: none;
    }
  }

  p {
    font-size: 1.2rem;
    color: #4a5568;
    max-width: 600px;
    margin: 0 auto;
    font-weight: 500;
  }

  @media (max-width: 768px) {
    padding: 2rem 1rem;

    h1 {
      font-size: 2rem;
    }

    p {
      font-size: 1rem;
    }
  }
`

const SearchContainer = styled.div`
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr;
  }
`

const FilterSidebar = styled.div`
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    display: ${({ isOpen }) => (isOpen ? 'block' : 'none')};
  }
`

const FilterCard = styled.div`
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 8px 25px rgba(68, 154, 164, 0.15);
  }
`

const FilterTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  display: flex;
  align-items: center;
  
  svg {
    margin-right: 0.5rem;
  }
`

const FilterGroup = styled.div`
  margin-bottom: 1.5rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`

const FilterLabel = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
`

const FilterInput = styled.input`
  width: 100%;
  padding: 0.8rem;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 5px;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`

const FilterSelect = styled.select`
  width: 100%;
  padding: 0.8rem;
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 5px;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary};
  }
`

const RangeContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`

const RangeInput = styled.input`
  width: 100%;
  margin: 1rem 0;
`

const RangeValue = styled.div`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.primary};
`

const CheckboxGroup = styled.div`
  margin-bottom: 0.5rem;
`

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  cursor: pointer;
  
  input {
    margin-right: 0.5rem;
  }
`

const ResultsContainer = styled.div`
  
`

const ResultsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
`

const ResultsCount = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 500;
`

const SortContainer = styled.div`
  display: flex;
  align-items: center;
  
  label {
    margin-right: 0.5rem;
  }
  
  select {
    padding: 0.5rem;
    border: 1px solid ${({ theme }) => theme.colors.border};
    border-radius: 5px;
    
    &:focus {
      outline: none;
      border-color: ${({ theme }) => theme.colors.primary};
    }
  }
`

const FilterToggle = styled.button`
  display: none;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  
  svg {
    margin-right: 0.5rem;
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    display: flex;
  }
`

const DestinationsList = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
`

const DestinationCard = styled.div`
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: grid;
  grid-template-columns: 300px 1fr;
  transition: all 0.3s ease;

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    grid-template-columns: 1fr;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(68, 154, 164, 0.2);
    border-color: rgba(68, 154, 164, 0.3);
  }
`

const CardImage = styled.div`
  height: 100%;
  min-height: 200px;
  background-image: ${({ image }) => `url(${image})`};
  background-size: cover;
  background-position: center;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    height: 200px;
  }
`

const CardContent = styled.div`
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
`

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    flex-direction: column;
    gap: 0.5rem;
  }
`

const CardTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  margin-bottom: 0.3rem;
`

const CardLocation = styled.div`
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors.textLight};
  
  svg {
    margin-right: 0.3rem;
  }
`

const CardPrice = styled.div`
  text-align: right;
  
  .price {
    font-size: ${({ theme }) => theme.fontSizes.xl};
    font-weight: 700;
    color: ${({ theme }) => theme.colors.primary};
  }
  
  .price-info {
    font-size: ${({ theme }) => theme.fontSizes.sm};
    color: ${({ theme }) => theme.colors.textLight};
  }
`

const CardDescription = styled.p`
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 1rem;
  flex-grow: 1;
`

const CardFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
`

const CardMeta = styled.div`
  display: flex;
  gap: 1rem;
`

const CardRating = styled.div`
  display: flex;
  align-items: center;
  
  svg {
    color: ${({ theme }) => theme.colors.warning};
    margin-right: 0.3rem;
  }
`

const CardDuration = styled.div`
  display: flex;
  align-items: center;
  
  svg {
    color: ${({ theme }) => theme.colors.textLight};
    margin-right: 0.3rem;
  }
`

const ViewButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 5px;
  font-weight: 500;
  transition: ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }
`

const NoResults = styled.div`
  text-align: center;
  padding: 3rem;
  background-color: white;
  border-radius: 10px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  
  h3 {
    font-size: ${({ theme }) => theme.fontSizes['2xl']};
    margin-bottom: 1rem;
  }
  
  p {
    color: ${({ theme }) => theme.colors.textLight};
    margin-bottom: 1.5rem;
  }
`

const SearchResults = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const queryParams = new URLSearchParams(location.search)
  
  const [filters, setFilters] = useState({
    destination: queryParams.get('destination') || '',
    date: queryParams.get('date') || '',
    travelers: queryParams.get('travelers') || '',
    minPrice: 0,
    maxPrice: 10000,
    duration: '',
    rating: ''
  })
  
  const [sortBy, setSortBy] = useState('recommended')
  const [filteredDestinations, setFilteredDestinations] = useState([])
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  
  useEffect(() => {
    applyFilters()
  }, [filters, sortBy])
  
  const applyFilters = () => {
    let results = [...destinations]
    
    // Appliquer les filtres
    if (filters.destination) {
      const searchTerm = filters.destination.toLowerCase()
      results = results.filter(dest => 
        dest.name.toLowerCase().includes(searchTerm) || 
        dest.description.toLowerCase().includes(searchTerm)
      )
    }
    
    if (filters.minPrice || filters.maxPrice) {
      results = results.filter(dest => 
        dest.price >= filters.minPrice && dest.price <= filters.maxPrice
      )
    }
    
    if (filters.duration) {
      results = results.filter(dest => {
        if (filters.duration === 'short') return dest.duration <= 3
        if (filters.duration === 'medium') return dest.duration > 3 && dest.duration <= 7
        if (filters.duration === 'long') return dest.duration > 7
        return true
      })
    }
    
    if (filters.rating) {
      const minRating = parseFloat(filters.rating)
      results = results.filter(dest => dest.rating >= minRating)
    }
    
    // Appliquer le tri
    if (sortBy === 'price-asc') {
      results.sort((a, b) => a.price - b.price)
    } else if (sortBy === 'price-desc') {
      results.sort((a, b) => b.price - a.price)
    } else if (sortBy === 'rating') {
      results.sort((a, b) => b.rating - a.rating)
    } else if (sortBy === 'duration') {
      results.sort((a, b) => a.duration - b.duration)
    }
    
    setFilteredDestinations(results)
  }
  
  const handleFilterChange = (e) => {
    const { name, value } = e.target
    setFilters(prev => ({ ...prev, [name]: value }))
  }
  
  const handleSortChange = (e) => {
    setSortBy(e.target.value)
  }
  
  const toggleFilter = () => {
    setIsFilterOpen(!isFilterOpen)
  }
  
  return (
    <>
      <HeroSection />

      <PageWrapper>
        <PageHeader>
          <h1>Découvrez Nos Destinations</h1>
          <p>Explorez les merveilles du Maroc et du monde entier avec ZiriTravel</p>
        </PageHeader>

        <div style={{ padding: '0 2rem' }}>
          <FilterToggle onClick={toggleFilter}>
            <FaFilter />
            {isFilterOpen ? 'Masquer les filtres' : 'Afficher les filtres'}
          </FilterToggle>

          <SearchContainer>
        <FilterSidebar isOpen={isFilterOpen}>
          <FilterCard>
            <FilterTitle>
              <FaSearch />
              Recherche
            </FilterTitle>
            <FilterGroup>
              <FilterLabel>Destination</FilterLabel>
              <FilterInput
                type="text"
                name="destination"
                placeholder="Où souhaitez-vous aller?"
                value={filters.destination}
                onChange={handleFilterChange}
              />
            </FilterGroup>
            <FilterGroup>
              <FilterLabel>Date de départ</FilterLabel>
              <FilterInput
                type="date"
                name="date"
                value={filters.date}
                onChange={handleFilterChange}
              />
            </FilterGroup>
            <FilterGroup>
              <FilterLabel>Voyageurs</FilterLabel>
              <FilterSelect
                name="travelers"
                value={filters.travelers}
                onChange={handleFilterChange}
              >
                <option value="">Tous</option>
                <option value="1">1 personne</option>
                <option value="2">2 personnes</option>
                <option value="3">3 personnes</option>
                <option value="4">4 personnes</option>
                <option value="5+">5+ personnes</option>
              </FilterSelect>
            </FilterGroup>
          </FilterCard>
          
          <FilterCard>
            <FilterTitle>
              <FaFilter />
              Filtres
            </FilterTitle>
            <FilterGroup>
              <FilterLabel>Budget (MAD)</FilterLabel>
              <RangeContainer>
                <span>0</span>
                <RangeValue>{filters.maxPrice}</RangeValue>
              </RangeContainer>
              <RangeInput
                type="range"
                name="maxPrice"
                min="0"
                max="10000"
                step="500"
                value={filters.maxPrice}
                onChange={handleFilterChange}
              />
            </FilterGroup>
            <FilterGroup>
              <FilterLabel>Durée</FilterLabel>
              <FilterSelect
                name="duration"
                value={filters.duration}
                onChange={handleFilterChange}
              >
                <option value="">Toutes les durées</option>
                <option value="short">Court séjour (1-3 jours)</option>
                <option value="medium">Séjour moyen (4-7 jours)</option>
                <option value="long">Long séjour (8+ jours)</option>
              </FilterSelect>
            </FilterGroup>
            <FilterGroup>
              <FilterLabel>Évaluation minimum</FilterLabel>
              <FilterSelect
                name="rating"
                value={filters.rating}
                onChange={handleFilterChange}
              >
                <option value="">Toutes les évaluations</option>
                <option value="4.5">Excellent (4.5+)</option>
                <option value="4">Très bon (4+)</option>
                <option value="3.5">Bon (3.5+)</option>
                <option value="3">Moyen (3+)</option>
              </FilterSelect>
            </FilterGroup>
          </FilterCard>
        </FilterSidebar>
        
        <ResultsContainer>
          <ResultsHeader>
            <ResultsCount>
              {filteredDestinations.length} {filteredDestinations.length > 1 ? 'résultats trouvés' : 'résultat trouvé'}
            </ResultsCount>
            <SortContainer>
              <label>Trier par:</label>
              <select value={sortBy} onChange={handleSortChange}>
                <option value="recommended">Recommandés</option>
                <option value="price-asc">Prix (croissant)</option>
                <option value="price-desc">Prix (décroissant)</option>
                <option value="rating">Évaluation</option>
                <option value="duration">Durée</option>
              </select>
            </SortContainer>
          </ResultsHeader>
          
          {filteredDestinations.length > 0 ? (
            <DestinationsList>
              {filteredDestinations.map(destination => (
                <DestinationCard key={destination.id}>
                  <CardImage image={destination.image} />
                  <CardContent>
                    <CardHeader>
                      <div>
                        <CardTitle>{destination.name}</CardTitle>
                        <CardLocation>
                          <FaMapMarkerAlt />
                          <span>Maroc</span>
                        </CardLocation>
                      </div>
                      <CardPrice>
                        <div className="price">{destination.price} MAD</div>
                        <div className="price-info">par personne</div>
                      </CardPrice>
                    </CardHeader>
                    <CardDescription>{destination.shortDescription}</CardDescription>
                    <CardFooter>
                      <CardMeta>
                        <CardRating>
                          <FaStar />
                          <span>{destination.rating}</span>
                        </CardRating>
                        <CardDuration>
                          <FaClock />
                          <span>{destination.duration} jours</span>
                        </CardDuration>
                      </CardMeta>
                      <ViewButton onClick={() => navigate(`/trip/${destination.id}`)}>
                        Voir détails
                      </ViewButton>
                    </CardFooter>
                  </CardContent>
                </DestinationCard>
              ))}
            </DestinationsList>
          ) : (
            <NoResults>
              <h3>Aucun résultat trouvé</h3>
              <p>Essayez de modifier vos critères de recherche pour trouver des voyages correspondants.</p>
              <button className="btn btn-primary" onClick={() => setFilters({
                destination: '',
                date: '',
                travelers: '',
                minPrice: 0,
                maxPrice: 10000,
                duration: '',
                rating: ''
              })}>
                Réinitialiser les filtres
              </button>
            </NoResults>
          )}
        </ResultsContainer>
      </SearchContainer>
        </div>
      </PageWrapper>
    </>
  )
}

export default SearchResults

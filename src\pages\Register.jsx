import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { <PERSON><PERSON><PERSON>ser, FaEnvelope, FaLock, FaPhone, FaFacebook, FaGoogle, FaArrowLeft, FaEye, FaEyeSlash } from 'react-icons/fa'

// Container principal moderne et propre
const RegisterPageContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  padding: 1rem;
  position: relative;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
`

// Conteneur principal de la carte d'inscription
const RegisterCard = styled.div`
  background: white;
  border-radius: 16px;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 500px;
  padding: 2rem;
  position: relative;
  z-index: 10;

  @media (max-width: 768px) {
    padding: 1.5rem;
    margin: 1rem;
    border-radius: 12px;
  }
`

// En-tête avec logo et titre
const RegisterHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`

const Logo = styled.div`
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #449aa4 0%, #2c7a7b 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;

  svg {
    font-size: 1.5rem;
    color: white;
  }
`

const Title = styled.h1`
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.25rem;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`

const Subtitle = styled.p`
  color: #718096;
  font-size: 0.875rem;
  margin-bottom: 0;
  font-weight: 400;
`



// Styles pour le formulaire
const RegisterForm = styled.form`
  display: flex;
  flex-direction: column;
`

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`

const FormGroup = styled.div`
  margin-bottom: 1.25rem;
`

const FormLabel = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`

const InputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;

  svg:first-child {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1rem;
    z-index: 2;
    pointer-events: none;
  }
`

const FormInput = styled.input`
  width: 100%;
  padding: 0.75rem 2.75rem 0.75rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &::placeholder {
    color: #9ca3af;
    font-weight: 400;
  }

  &:focus {
    outline: none;
    border-color: #449aa4;
    box-shadow: 0 0 0 3px rgba(68, 154, 164, 0.1);
  }

  &:hover {
    border-color: #9ca3af;
  }

  /* Style spécial pour les champs sans bouton toggle */
  &:not([type="password"]):not([data-has-toggle="true"]) {
    padding-right: 0.75rem;
  }
`

const PasswordToggle = styled.button`
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;

  &:hover {
    color: #449aa4;
    background-color: rgba(68, 154, 164, 0.1);
  }

  &:focus {
    outline: none;
    color: #449aa4;
    background-color: rgba(68, 154, 164, 0.1);
  }

  svg {
    font-size: 0.875rem;
  }
`

// Boutons sociaux
const SocialLoginSection = styled.div`
  margin-bottom: 1.5rem;
`

const SocialLoginText = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  text-align: center;
  font-weight: 400;
`

const SocialButtons = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
`

const SocialButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  font-weight: 500;
  gap: 0.5rem;

  &:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
  }

  &:active {
    transform: scale(0.98);
  }

  &.google {
    color: #ea4335;
  }

  &.facebook {
    color: #1877f2;
  }
`

const OrDivider = styled.div`
  display: flex;
  align-items: center;
  margin: 1.5rem 0;

  &::before, &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e5e7eb;
  }

  span {
    padding: 0 1rem;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 400;
    background: white;
  }
`

const TermsCheckbox = styled.label`
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.4;

  input {
    margin-right: 0.5rem;
    margin-top: 0.1rem;
    accent-color: #449aa4;
  }

  a {
    color: #449aa4;
    font-weight: 500;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`

const RegisterButton = styled.button`
  width: 100%;
  background: #449aa4;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1rem;

  &:hover {
    background: #2c7a7b;
  }

  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }
`

const LoginLink = styled.div`
  text-align: center;
  margin-top: 1rem;
  font-size: 0.875rem;
  color: #6b7280;

  a {
    color: #449aa4;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover {
      text-decoration: underline;
    }
  }
`

const ErrorMessage = styled.div`
  background: #fef2f2;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #fecaca;
`

const SuccessMessage = styled.div`
  background: #f0fdf4;
  color: #16a34a;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #bbf7d0;
`

// Composant principal
const Register = () => {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false
  })
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    // Clear messages when user starts typing
    if (error) setError('')
    if (success) setSuccess('')
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setSuccess('')

    // Validation
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
      setError('Veuillez remplir tous les champs obligatoires')
      setIsLoading(false)
      return
    }

    // Validation email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      setError('Veuillez entrer une adresse email valide')
      setIsLoading(false)
      return
    }

    // Validation mot de passe
    if (formData.password.length < 6) {
      setError('Le mot de passe doit contenir au moins 6 caractères')
      setIsLoading(false)
      return
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Les mots de passe ne correspondent pas')
      setIsLoading(false)
      return
    }

    if (!formData.agreeTerms) {
      setError('Vous devez accepter les conditions générales')
      setIsLoading(false)
      return
    }

    // Simulation d'inscription réussie
    setTimeout(() => {
      setSuccess('Compte créé avec succès ! Redirection vers la connexion...')
      setTimeout(() => {
        setIsLoading(false)
        navigate('/login')
      }, 1000)
    }, 1500)
  }

  const handleSocialRegister = (provider) => {
    console.log(`Inscription avec ${provider}`)
    setSuccess(`Redirection vers ${provider}...`)
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword)
  }

  return (
    <RegisterPageContainer>
      <RegisterCard>
        <RegisterHeader>
          <Logo>
            <FaUser />
          </Logo>
          <Title>ZiriTravel</Title>
          <Subtitle>Créez votre compte pour commencer</Subtitle>
        </RegisterHeader>

        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}

        <SocialLoginSection>
          <SocialLoginText>Inscription rapide avec</SocialLoginText>
          <SocialButtons>
            <SocialButton
              type="button"
              className="google"
              onClick={() => handleSocialRegister('Google')}
            >
              <FaGoogle />
              Google
            </SocialButton>
            <SocialButton
              type="button"
              className="facebook"
              onClick={() => handleSocialRegister('Facebook')}
            >
              <FaFacebook />
              Facebook
            </SocialButton>
          </SocialButtons>
        </SocialLoginSection>

        <OrDivider>
          <span>ou avec votre email</span>
        </OrDivider>

        <RegisterForm onSubmit={handleSubmit}>
          <FormRow>
            <FormGroup>
              <FormLabel>Prénom *</FormLabel>
              <InputWrapper>
                <FaUser />
                <FormInput
                  type="text"
                  name="firstName"
                  placeholder="Votre prénom"
                  value={formData.firstName}
                  onChange={handleChange}
                  required
                />
              </InputWrapper>
            </FormGroup>

            <FormGroup>
              <FormLabel>Nom *</FormLabel>
              <InputWrapper>
                <FaUser />
                <FormInput
                  type="text"
                  name="lastName"
                  placeholder="Votre nom"
                  value={formData.lastName}
                  onChange={handleChange}
                  required
                />
              </InputWrapper>
            </FormGroup>
          </FormRow>

          <FormGroup>
            <FormLabel>Adresse email *</FormLabel>
            <InputWrapper>
              <FaEnvelope />
              <FormInput
                type="email"
                name="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </InputWrapper>
          </FormGroup>

          <FormGroup>
            <FormLabel>Téléphone</FormLabel>
            <InputWrapper>
              <FaPhone />
              <FormInput
                type="tel"
                name="phone"
                placeholder="+212 6 00 00 00 00"
                value={formData.phone}
                onChange={handleChange}
              />
            </InputWrapper>
          </FormGroup>

          <FormRow>
            <FormGroup>
              <FormLabel>Mot de passe *</FormLabel>
              <InputWrapper>
                <FaLock />
                <FormInput
                  type={showPassword ? "text" : "password"}
                  name="password"
                  placeholder="Votre mot de passe"
                  value={formData.password}
                  onChange={handleChange}
                  data-has-toggle="true"
                  required
                />
                <PasswordToggle
                  type="button"
                  onClick={togglePasswordVisibility}
                  aria-label="Afficher/masquer le mot de passe"
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </PasswordToggle>
              </InputWrapper>
            </FormGroup>

            <FormGroup>
              <FormLabel>Confirmer le mot de passe *</FormLabel>
              <InputWrapper>
                <FaLock />
                <FormInput
                  type={showConfirmPassword ? "text" : "password"}
                  name="confirmPassword"
                  placeholder="Confirmez le mot de passe"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  data-has-toggle="true"
                  required
                />
                <PasswordToggle
                  type="button"
                  onClick={toggleConfirmPasswordVisibility}
                  aria-label="Afficher/masquer la confirmation du mot de passe"
                >
                  {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                </PasswordToggle>
              </InputWrapper>
            </FormGroup>
          </FormRow>

          <TermsCheckbox>
            <input
              type="checkbox"
              name="agreeTerms"
              checked={formData.agreeTerms}
              onChange={handleChange}
              required
            />
            <span>
              J'accepte les <Link to="/terms">conditions générales</Link> et la <Link to="/privacy">politique de confidentialité</Link>
            </span>
          </TermsCheckbox>

          <RegisterButton type="submit" disabled={isLoading}>
            {isLoading ? "Création du compte..." : "Créer mon compte"}
          </RegisterButton>
        </RegisterForm>

        <LoginLink>
          Vous avez déjà un compte ? <Link to="/login">Se connecter</Link>
        </LoginLink>
      </RegisterCard>
    </RegisterPageContainer>
  )
}

export default Register

import { Link } from 'react-router-dom'
import styled from 'styled-components'
import { FaFacebook, FaTwitter, FaInstagram, FaYoutube, FaMapMarkerAlt, FaPhone, FaEnvelope } from 'react-icons/fa'

const FooterContainer = styled.footer`
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #449aa4 75%, #2c7a7b 100%);
  color: white;
  padding: 4rem 0 0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  }

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(68, 154, 164, 0.1) 0%, transparent 70%);
    animation: float 20s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
  }
`

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 3rem;
  position: relative;
  z-index: 2;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 1.5rem;
  }
`

const FooterColumn = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
`

const FooterTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
  color: #ffffff;

  &::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
  }
`

const FooterDescription = styled.p`
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
  font-size: 0.95rem;
`

const FooterLink = styled(Link)`
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  position: relative;
  padding: 0.25rem 0;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #FFD700, #FFA500);
    transition: width 0.3s ease;
  }

  &:hover {
    color: #FFD700;
    transform: translateX(8px);

    &::before {
      width: 30px;
    }
  }

  svg {
    margin-right: 0.5rem;
    font-size: 0.8rem;
  }
`

const ContactItem = styled.div`
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.25rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  svg {
    margin-right: 1rem;
    margin-top: 0.2rem;
    color: #FFD700;
    font-size: 1.1rem;
    flex-shrink: 0;
  }

  div {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    line-height: 1.4;
  }
`

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
`

const SocialLink = styled.a`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 1.1rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
    border-color: #FFD700;

    &::before {
      left: 100%;
    }
  }

  &:nth-child(1):hover { background: linear-gradient(135deg, #3b5998, #4c70ba); }
  &:nth-child(2):hover { background: linear-gradient(135deg, #1da1f2, #0d8bd9); }
  &:nth-child(3):hover { background: linear-gradient(135deg, #e4405f, #fd1d1d); }
  &:nth-child(4):hover { background: linear-gradient(135deg, #ff0000, #cc0000); }
`

const Copyright = styled.div`
  text-align: center;
  margin-top: 3rem;
  padding: 2rem 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 1px;
    background: linear-gradient(90deg, transparent, #FFD700, transparent);
  }
`

const BrandName = styled.span`
  color: #FFD700;
  font-weight: 700;
  font-size: 1rem;
`

const Footer = () => {
  return (
    <FooterContainer>
      <FooterContent>
        <FooterColumn>
          <FooterTitle>À propos de ZiriTravel</FooterTitle>
          <FooterDescription>
            Chez ZiriTravel, nous pensons que chaque voyage est une opportunité d'expériences uniques et sur mesure. Notre équipe de passionnés est dévouée à vous offrir des souvenirs inoubliables. Faites-nous confiance pour être votre compagnon de voyage et séjour idéal.
          </FooterDescription>
          <SocialLinks>
            <SocialLink href="#" target="_blank" rel="noopener noreferrer" title="Facebook">
              <FaFacebook />
            </SocialLink>
            <SocialLink href="#" target="_blank" rel="noopener noreferrer" title="Twitter">
              <FaTwitter />
            </SocialLink>
            <SocialLink href="#" target="_blank" rel="noopener noreferrer" title="Instagram">
              <FaInstagram />
            </SocialLink>
            <SocialLink href="#" target="_blank" rel="noopener noreferrer" title="YouTube">
              <FaYoutube />
            </SocialLink>
          </SocialLinks>
        </FooterColumn>

        <FooterColumn>
          <FooterTitle>Navigation</FooterTitle>
          <FooterLink to="/">Accueil</FooterLink>
          <FooterLink to="/search">Destinations</FooterLink>
          <FooterLink to="/faq">FAQ</FooterLink>
          <FooterLink to="/login">Mon compte</FooterLink>
          <FooterLink to="/cart">Panier</FooterLink>
        </FooterColumn>

        <FooterColumn>
          <FooterTitle>Destinations</FooterTitle>
          <FooterLink to="/search?destination=marrakech">Marrakech</FooterLink>
          <FooterLink to="/search?destination=casablanca">Casablanca</FooterLink>
          <FooterLink to="/search?destination=fes">Fès</FooterLink>
          <FooterLink to="/search?destination=chefchaouen">Chefchaouen</FooterLink>
          <FooterLink to="/search?destination=essaouira">Essaouira</FooterLink>
        </FooterColumn>

        <FooterColumn>
          <FooterTitle>Contactez-nous</FooterTitle>
          <ContactItem>
            <FaMapMarkerAlt />
            <div>123 Avenue Mohammed V<br />Casablanca, Maroc</div>
          </ContactItem>
          <ContactItem>
            <FaPhone />
            <div>+212 522 123 456</div>
          </ContactItem>
          <ContactItem>
            <FaEnvelope />
            <div><EMAIL></div>
          </ContactItem>
        </FooterColumn>
      </FooterContent>

      <Copyright>
        &copy; {new Date().getFullYear()} <BrandName>ZiriTravel</BrandName>. Tous droits réservés.
      </Copyright>
    </FooterContainer>
  )
}

export default Footer

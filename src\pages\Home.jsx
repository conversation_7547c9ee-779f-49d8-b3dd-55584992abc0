import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { destinationService } from '../services/api'
import { testDestinations } from '../data/testDestinations'
import LoadingSpinner from '../components/LoadingSpinner'
import DestinationImage from '../components/DestinationImage'
import { FaSearch, FaCalendarAlt, FaStar, FaMapMarkerAlt, FaClock } from 'react-icons/fa'

const HeroSection = styled.section`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5)),
    url('https://images.pexels.com/photos/11107457/pexels-photo-11107457.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
  z-index: -1;

  @media (max-width: 768px) {
    background-attachment: scroll;
  }
`

const ContentWrapper = styled.div`
  position: relative;
  z-index: 1;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 6rem 2rem 4rem;

  @media (max-width: 768px) {
    padding: 5rem 1.5rem 3rem;
  }

  @media (max-width: 480px) {
    padding: 4rem 1rem 2rem;
  }
`

const HeroTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['5xl']};
  margin-bottom: 1rem;
  font-weight: 700;
  color: #FFD700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    font-size: ${({ theme }) => theme.fontSizes['3xl']};
  }
`

const HeroSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  margin-bottom: 2rem;
  max-width: 900px;
  line-height: 1.6;
  font-weight: 400;
  position: relative;
  z-index: 2;
  color: #F0F8FF;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    font-size: ${({ theme }) => theme.fontSizes.lg};
    max-width: 100%;
  }
`

const SearchForm = styled.form`
  background-color: white;
  padding: 2rem;
  border-radius: 10px;
  width: 100%;
  max-width: 900px;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  box-shadow: ${({ theme }) => theme.shadows.lg};

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    flex-direction: column;
  }
`

const SearchInput = styled.div`
  flex: 1;
  min-width: 200px;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: ${({ theme }) => theme.colors.text};
  }

  .input-wrapper {
    position: relative;

    svg {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: ${({ theme }) => theme.colors.textLight};
    }
  }

  input, select {
    width: 100%;
    padding: 0.8rem 1rem 0.8rem 2.5rem;
    border: 1px solid ${({ theme }) => theme.colors.border};
    border-radius: 5px;
    font-size: ${({ theme }) => theme.fontSizes.md};

    &:focus {
      outline: none;
      border-color: ${({ theme }) => theme.colors.primary};
    }
  }
`

const SearchButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  border: none;
  border-radius: 5px;
  padding: 0.8rem 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: ${({ theme }) => theme.transitions.fast};
  align-self: flex-end;
  margin-top: 1.5rem;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primaryDark};
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    width: 100%;
  }
`

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  font-weight: 800;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

  /* Fallback pour les navigateurs qui ne supportent pas background-clip */
  @supports not (-webkit-background-clip: text) {
    color: #2c3e50;
    background: none;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #449aa4, #FFD700, #449aa4);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(68, 154, 164, 0.3);
  }

  &::before {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.6), transparent);
    border-radius: 1px;
  }

  @media (max-width: 768px) {
    font-size: 2rem;
    margin-bottom: 2.5rem;
  }

  @media (max-width: 480px) {
    font-size: 1.75rem;
    margin-bottom: 2rem;
  }
`

const DestinationsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`

const DestinationCard = styled.div`
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows.md};
  transition: ${({ theme }) => theme.transitions.normal};

  &:hover {
    transform: translateY(-5px);
    box-shadow: ${({ theme }) => theme.shadows.lg};
  }
`

const CardImage = styled.div`
  height: 200px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  background-color: #f0f0f0;
  overflow: hidden;

  /* Effet de survol */
  &:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
  }

  /* Overlay pour améliorer la lisibilité du prix */
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
    pointer-events: none;
  }
`

const CardPrice = styled.div`
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background-color: ${({ theme }) => theme.colors.primary};
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  font-weight: 600;
`

const CardContent = styled.div`
  padding: 1.5rem;
`

const CardTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  margin-bottom: 0.5rem;
`

const CardDescription = styled.p`
  color: ${({ theme }) => theme.colors.textLight};
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`

const CardMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
`

const CardRating = styled.div`
  display: flex;
  align-items: center;

  svg {
    color: ${({ theme }) => theme.colors.warning};
    margin-right: 0.3rem;
  }
`

const CardDuration = styled.div`
  display: flex;
  align-items: center;

  svg {
    color: ${({ theme }) => theme.colors.textLight};
    margin-right: 0.3rem;
  }
`

const ViewAllButton = styled.button`
  display: block;
  margin: 3rem auto 0;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
  border: none;
  color: white;
  padding: 0.8rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

  &:hover {
    background: linear-gradient(135deg, #FFA500 0%, #FF8C00 50%, #FF6347 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 165, 0, 0.4);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
  }
`

const PromotionSection = styled.section`
  background-color: ${({ theme }) => theme.colors.backgroundAlt};
  padding: 4rem 0;
  margin-top: 4rem;
`

const PromotionCard = styled.div`
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1548784764-2d2c4e6bc875');
  background-size: cover;
  background-position: center;
  border-radius: 10px;
  padding: 3rem;
  color: white;
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
`

const PromotionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  margin-bottom: 1rem;
`

const PromotionDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`

const PromotionButton = styled.button`
  background-color: ${({ theme }) => theme.colors.secondary};
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 5px;
  font-weight: 600;
  transition: ${({ theme }) => theme.transitions.fast};

  &:hover {
    background-color: ${({ theme }) => theme.colors.secondaryDark};
  }
`

const Home = () => {
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useState({
    destination: '',
    date: '',
    travelers: '2'
  })
  const [destinations, setDestinations] = useState(testDestinations.slice(0, 6))
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Essayer de récupérer les destinations depuis l'API (optionnel)
  useEffect(() => {
    const fetchPopularDestinations = async () => {
      try {
        console.log('🔄 Tentative de chargement depuis l\'API...')
        const response = await destinationService.getPopular()

        if (response && response.success && response.data && Array.isArray(response.data) && response.data.length > 0) {
          setDestinations(response.data)
          setError(null)
          console.log('✅ Destinations chargées depuis l\'API:', response.data.length)
        } else {
          console.log('⚠️ API non disponible, utilisation des données de démonstration')
        }
      } catch (err) {
        console.log('⚠️ Backend non disponible, utilisation des données de démonstration')
        // Les données de test sont déjà chargées par défaut, pas besoin de les recharger
      }
    }

    fetchPopularDestinations()
  }, [])

  const popularDestinations = destinations

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setSearchParams(prev => ({ ...prev, [name]: value }))
  }

  const handleSearch = (e) => {
    e.preventDefault()
    navigate(`/search?destination=${searchParams.destination}&date=${searchParams.date}&travelers=${searchParams.travelers}`)
  }

  const handleViewAll = () => {
    navigate('/search')
  }

  return (
    <>
      <HeroSection />

      <ContentWrapper>
        <HeroTitle>Découvrez le Maroc Authentique</HeroTitle>
        <HeroSubtitle>
          Explorez le monde autrement avec ZiriTravel, votre partenaire de confiance pour des voyages et séjours inoubliables. Que vous rêviez de découvrir les trésors du Maroc ou de partir à l'aventure à l'international, nous vous proposons une large sélection de destinations et de circuits adaptés à tous les goûts et à tous les budgets.
        </HeroSubtitle>

        <SearchForm onSubmit={handleSearch}>
          <SearchInput>
            <label>Destination</label>
            <div className="input-wrapper">
              <FaMapMarkerAlt />
              <input
                type="text"
                name="destination"
                placeholder="Où souhaitez-vous aller?"
                value={searchParams.destination}
                onChange={handleInputChange}
              />
            </div>
          </SearchInput>

          <SearchInput>
            <label>Date</label>
            <div className="input-wrapper">
              <FaCalendarAlt />
              <input
                type="date"
                name="date"
                value={searchParams.date}
                onChange={handleInputChange}
              />
            </div>
          </SearchInput>

          <SearchInput>
            <label>Voyageurs</label>
            <div className="input-wrapper">
              <FaSearch />
              <select
                name="travelers"
                value={searchParams.travelers}
                onChange={handleInputChange}
              >
                <option value="1">1 personne</option>
                <option value="2">2 personnes</option>
                <option value="3">3 personnes</option>
                <option value="4">4 personnes</option>
                <option value="5+">5+ personnes</option>
              </select>
            </div>
          </SearchInput>

          <SearchButton type="submit">Rechercher</SearchButton>
        </SearchForm>
      </ContentWrapper>

      <section className="container">
        <SectionTitle>Destinations Populaires</SectionTitle>

        <DestinationsGrid>
          {popularDestinations.map(destination => (
            <DestinationCard key={destination.id} onClick={() => navigate(`/trip/${destination.id}`)}>
              <DestinationImage
                src={destination.image}
                alt={destination.name}
                cityName={destination.name}
              >
                <CardPrice>{destination.price} DH</CardPrice>
              </DestinationImage>
              <CardContent>
                <CardTitle>{destination.name}</CardTitle>
                <CardDescription>{destination.short_description}</CardDescription>
                <CardMeta>
                  <CardRating>
                    <FaStar />
                    <span>{destination.rating} ({destination.reviews_count})</span>
                  </CardRating>
                  <CardDuration>
                    <FaClock />
                    <span>{destination.duration} jours</span>
                  </CardDuration>
                </CardMeta>
              </CardContent>
            </DestinationCard>
          ))}
        </DestinationsGrid>

        <ViewAllButton onClick={handleViewAll}>
          Voir toutes les destinations
        </ViewAllButton>
      </section>

      <PromotionSection>
        <div className="container">
          <PromotionCard>
            <PromotionTitle>Offre Spéciale - Circuit Villes Impériales</PromotionTitle>
            <PromotionDescription>
              Découvrez les quatre villes impériales du Maroc : Marrakech, Fès, Meknès et Rabat.
              Profitez de 15% de réduction pour toute réservation avant le 30 juin.
            </PromotionDescription>
            <PromotionButton onClick={() => navigate('/search?promotion=imperial')}>
              En savoir plus
            </PromotionButton>
          </PromotionCard>
        </div>
      </PromotionSection>
    </>
  )
}

export default Home

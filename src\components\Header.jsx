import { useState } from 'react'
import { <PERSON>, useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>ser, FaShoppingCart, FaBars, FaTimes } from 'react-icons/fa'

const HeaderContainer = styled.header`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
`

const NavContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`

const Logo = styled(Link)`
  font-size: 1.8rem;
  font-weight: 800;
  color: #449aa4;
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #449aa4, #2c7a7b);
    transition: width 0.3s ease;
  }

  &:hover {
    transform: translateY(-1px);
    color: #2c7a7b;

    &::after {
      width: 100%;
    }
  }

  span {
    color: #e53e3e;
    margin-left: 2px;
  }
`

const NavLinks = styled.nav`
  display: flex;
  align-items: center;
  gap: 2rem;

  @media (max-width: 768px) {
    position: fixed;
    top: 0;
    right: ${({ isOpen }) => (isOpen ? '0' : '-100%')};
    width: 75%;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 6rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: ${({ isOpen }) => (isOpen ? '-10px 0 30px rgba(0, 0, 0, 0.1)' : 'none')};
    border-left: 1px solid rgba(0, 0, 0, 0.1);
  }
`

const NavLink = styled(Link)`
  font-weight: 600;
  color: #2d3748;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  font-size: 0.95rem;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #449aa4, #2c7a7b);
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  &:hover {
    color: #449aa4;
    background: rgba(68, 154, 164, 0.1);
    transform: translateY(-1px);

    &::before {
      width: 80%;
    }
  }

  @media (max-width: 768px) {
    margin: 0.5rem 0;
    font-size: 1.1rem;
    padding: 1rem 2rem;
    width: 80%;
    text-align: center;
  }
`

const NavIcons = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`

const IconLink = styled(Link)`
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: #4a5568;
  background: rgba(68, 154, 164, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  text-decoration: none;

  &:hover {
    color: white;
    background: #449aa4;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(68, 154, 164, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
`

const CartCount = styled.span`
  position: absolute;
  top: -6px;
  right: -6px;
  background: linear-gradient(135deg, #e53e3e, #c53030);
  color: white;
  font-size: 0.7rem;
  font-weight: 700;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
`

const MenuButton = styled.button`
  display: none;
  width: 44px;
  height: 44px;
  background: rgba(68, 154, 164, 0.1);
  border: none;
  border-radius: 12px;
  font-size: 1.2rem;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 200;

  &:hover {
    background: #449aa4;
    color: white;
    transform: translateY(-1px);
  }

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
`

const CloseButton = styled.button`
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  width: 40px;
  height: 40px;
  background: rgba(229, 62, 62, 0.1);
  border: none;
  border-radius: 10px;
  font-size: 1.2rem;
  color: #e53e3e;
  cursor: pointer;
  transition: all 0.3s ease;
  display: none;

  &:hover {
    background: #e53e3e;
    color: white;
    transform: rotate(90deg);
  }

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
`

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [cartItems, setCartItems] = useState(0) // Simuler des articles dans le panier
  const navigate = useNavigate()

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const closeMenu = () => {
    setIsMenuOpen(false)
  }

  return (
    <HeaderContainer>
      <NavContainer>
        <Logo to="/">
          Ziri<span>Travel</span>
        </Logo>

        <MenuButton onClick={toggleMenu}>
          <FaBars />
        </MenuButton>

        <NavLinks isOpen={isMenuOpen}>
          <CloseButton onClick={closeMenu}>
            <FaTimes />
          </CloseButton>
          <NavLink to="/" onClick={closeMenu}>Accueil</NavLink>
          <NavLink to="/search" onClick={closeMenu}>Destinations</NavLink>
          <NavLink to="/faq" onClick={closeMenu}>FAQ</NavLink>
        </NavLinks>

        <NavIcons>
          <IconLink to="/search">
            <FaSearch />
          </IconLink>
          <IconLink to="/login">
            <FaUser />
          </IconLink>
          <IconLink to="/cart">
            <FaShoppingCart />
            {cartItems > 0 && <CartCount>{cartItems}</CartCount>}
          </IconLink>
        </NavIcons>
      </NavContainer>
    </HeaderContainer>
  )
}

export default Header
